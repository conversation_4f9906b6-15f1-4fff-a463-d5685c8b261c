import OpenAI from 'openai';
import apiClient from '../lib/api_client.js';
import CONFIG from '../../config.json' with { type: 'json' };
import { SYSTEM_PROMPTS, generatePNLAnalysisPrompt } from '../utils/prompts.js';

const openai = new OpenAI({
  apiKey: CONFIG.OPENAI_API_KEY,
});

const cleanJsonResponse = (response) => {
  // Remove markdown code block syntax if present
  return response
    .replace(/^```json\n/, '')  // Remove opening ```json
    .replace(/^```\n/, '')      // Remove opening ``` without json
    .replace(/\n```$/, '')      // Remove closing ```
    .trim();                    // Remove any extra whitespace
};

const OPENAI_CONTROLLER = {
  analyzePNL: async (req, res, next) => {
    try {
      const { pnlDataArray, userSelections } = req.body;

      // Validate input
      if (!Array.isArray(pnlDataArray)) {
        return apiClient.invalidRequest(req, res, next, 'pnlDataArray must be an array');
      }

      if (pnlDataArray.length === 0) {
        return apiClient.invalidRequest(req, res, next, 'pnlDataArray cannot be empty');
      }

      // Process each PnL data object in order
      const analysisResults = [];

      for (let i = 0; i < pnlDataArray.length; i++) {
        const pnlData = pnlDataArray[i];

        try {
          const completion = await openai.chat.completions.create({
            model: "gpt-4-turbo-preview",
            messages: [
              {
                role: "system",
                content: SYSTEM_PROMPTS.FINANCIAL_ANALYST
              },
              {
                role: "user",
                content: generatePNLAnalysisPrompt({ pnlData, userSelections })
              }
            ],
            temperature: 0.2,
          });

          let parsedResponse;
          try {
            const responseContent = cleanJsonResponse(completion.choices[0].message.content);
            parsedResponse = JSON.parse(responseContent);

            // Validate response structure
            if (!parsedResponse.updatedPnL || !parsedResponse.analysis) {
              throw new Error('Invalid response structure');
            }

            // Validate updatedPnL is an array
            if (!Array.isArray(parsedResponse.updatedPnL)) {
              throw new Error('updatedPnL must be an array');
            }

            analysisResults.push(parsedResponse);

          } catch (parseError) {
            console.error(`OpenAI Response Parse Error for PnL ${i}:`, parseError);
            console.error('Raw Response:', completion.choices[0].message.content);
            return apiClient.serverError(req, res, next, `Failed to parse AI response for PnL ${i}`);
          }

        } catch (openaiError) {
          console.error(`OpenAI API Error for PnL ${i}:`, openaiError);
          return apiClient.serverError(req, res, next, `Failed to analyze PnL ${i}`);
        }
      }

      return apiClient.success(req, res, next, {
        analyses: analysisResults
      });
    } catch (error) {
      console.error('OpenAI Analysis Error:', error);
      return apiClient.serverError(req, res, next);
    }
  },
  analyzeTable: async (req, res, next) => {
    try {
      const { data } = req.body;

      const completion = await openai.chat.completions.create({
        model: "gpt-4-turbo-preview",
        messages: [
          {
            role: "system",
            content: `You are a financial data analyzer. Analyze the provided table data and identify key financial metrics. 
            Return ONLY a JSON object with the following structure:
            {
              "keyMetrics": {
                "revenue": { "original": number, "updated": number },
                "grossProfit": { "original": number, "updated": number },
                "operatingIncome": { "original": number, "updated": number },
              }
            }
            For each metric, extract both the original and updated values from the table. The table has three columns: 
            description, original amount, and updated amount. Identify equivalent terms (e.g., "Sales" for "Revenue").`
          },
          {
            role: "user",
            content: JSON.stringify(data)
          }
        ],
        temperature: 0.1,
      });

      let parsedResponse;
      try {
        const responseContent = cleanJsonResponse(completion.choices[0].message.content);
        parsedResponse = JSON.parse(responseContent);
        
        // Validate response structure
        if (!parsedResponse.keyMetrics) {
          throw new Error('Invalid response structure: missing keyMetrics');
        }

        const requiredMetrics = ['revenue', 'grossProfit', 'operatingIncome'];
        for (const metric of requiredMetrics) {
          if (!parsedResponse.keyMetrics[metric]) {
            throw new Error(`Invalid response structure: missing ${metric}`);
          }
          if (!('original' in parsedResponse.keyMetrics[metric]) || 
              !('updated' in parsedResponse.keyMetrics[metric])) {
            throw new Error(`Invalid response structure: missing original/updated values for ${metric}`);
          }
        }

      } catch (parseError) {
        console.error('OpenAI Response Parse Error:', parseError);
        console.error('Raw Response:', completion.choices[0].message.content);
        return apiClient.serverError(req, res, next, 'Failed to parse AI response');
      }

      return apiClient.success(req, res, next, parsedResponse);
    } catch (error) {
      console.error('OpenAI Table Analysis Error:', error);
      return apiClient.serverError(req, res, next);
    }
  },
};

export default OPENAI_CONTROLLER;
