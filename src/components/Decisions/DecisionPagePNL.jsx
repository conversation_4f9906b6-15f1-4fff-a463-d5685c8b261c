import React from 'react';
import { useTheme } from '@/components/ThemeProvider/ThemeProvider';

function DecisionPagePNL({ data, label, aiAnalysis, client }) {
  const { theme } = useTheme();
  if (!data?.data || !Array.isArray(data.data) || data.data.length === 0) {
    return null;
  }

  const headers = data.data[0];
  const rows = data.data.slice(1).filter((row) => Array.isArray(row) && row.length > 1);

  // Helper function to check if a row should be highlighted
  const isKeyPNLRow = (rowName) => {
    if (!rowName || typeof rowName !== 'string') return false;
    const name = rowName.toLowerCase().trim();
    return (
      name.includes('revenue') ||
      name.includes('gross margin') ||
      name.includes('operating profit') ||
      name.includes('gross profit') ||
      name === 'revenue' ||
      name === 'gross margin' ||
      name === 'operating profit' ||
      name === 'gross profit'
    );
  };

  return (
    <div className="mt-8">
      <h3 className={`text-xl font-semibold mb-4 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>{label || 'Page P&L'}</h3>

      {/* AI Analysis Section */}
      {aiAnalysis?.analysis && (
        <div className={`mb-6 p-4 rounded-lg ${theme === 'dark' ? 'bg-blue-900/20' : 'bg-blue-50'}`}>
          <h4 className={`text-lg font-medium mb-2 ${theme === 'dark' ? 'text-white' : 'text-gray-800'}`}>
            {client?.aiSummaryTitle || 'AI Analysis Summary'}
          </h4>
          <p className={`text-sm ${theme === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
            {aiAnalysis.analysis}
          </p>
        </div>
      )}
      <div className={`overflow-hidden ${theme === 'dark' ? 'bg-gray-700' : 'bg-gray-100'}`}>
        <table className="w-full">
          <thead>
            <tr className={`${theme === 'dark' ? 'bg-gray-800' : 'bg-gray-200'}`}>
              {headers.map((header, index) => (
                <th
                  key={index}
                  style={{
                    borderTopLeftRadius: index === 0 ? '8px' : '0',
                    borderTopRightRadius: index === headers.length - 1 ? '8px' : '0',
                    borderBottomLeftRadius: 0,
                    borderBottomRightRadius: 0,
                  }}
                  className={`py-3 px-4 ${theme === 'dark' ? 'text-gray-200' : 'text-gray-700'} ${index === 0 ? 'text-left' : 'text-right'}`}
                >
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {rows.map((row, rowIndex) => {
              const isKeyRow = isKeyPNLRow(row[0]);
              const baseClasses = `border-t ${theme === 'dark' ? 'border-gray-700' : 'border-gray-300'}`;
              const hoverClasses = theme === 'dark' ? 'hover:bg-gray-800/30' : 'hover:bg-gray-100/50';
              const highlightClasses = isKeyRow
                ? theme === 'dark'
                  ? 'bg-gray-600/70 hover:bg-gray-600/30'
                  : 'bg-blue-50 hover:bg-blue-100/70'
                : hoverClasses;

              return (
                <tr key={rowIndex} className={`${baseClasses} ${highlightClasses}`}>
                {row.map((cell, cellIndex) => {
                  const isNumeric = !isNaN(parseFloat(cell)) && isFinite(cell);
                  return (
                    <td
                      key={cellIndex}
                      className={`py-3 px-4 rounded-none ${cellIndex === 0 ? 'text-left' : 'text-right'} ${
                        isNumeric
                          ? parseFloat(cell) < 0
                            ? 'text-red-400'
                            : parseFloat(cell) > 0
                            ? 'text-green-400'
                            : theme === 'dark' ? 'text-gray-200' : 'text-gray-700'
                          : theme === 'dark' ? 'text-gray-200' : 'text-gray-700'
                      }`}
                    >
                      {cell}
                    </td>
                  );
                })}
              </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
}

export default DecisionPagePNL;
