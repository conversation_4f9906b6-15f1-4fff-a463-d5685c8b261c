import axios from 'axios';

import <PERSON>NF<PERSON> from '../config';

// sign in user
export const SIGNIN_USER = 'SIGNIN_USER';
export const SIGNIN_USER_SUCCESS = 'SIGNIN_USER_SUCCESS';
export const SIGNIN_USER_FAILURE = 'SIGNIN_USER_FAILURE';

// sign up user
export const SIGNUP_USER = 'SIGNUP_USER';
// forgot password
export const FORGOT_PASSWORD = 'FORGOT_PASSWORD';
export const RECOVER_PASSWORD = 'RECOVER_PASSWORD';

// sign out user
export const SIGNOUT_USER = 'SIGNOUT_USER';
export const RESET_TOKEN = 'RESET_TOKEN';

// token refresh
export const REFRESH_TOKEN = 'REFRESH_TOKEN';
export const TOKEN_REFRESH_SUCCESS = 'TOKEN_REFRESH_SUCCESS';
export const TOKEN_REFRESH_FAILURE = 'TOKEN_REFRESH_FAILURE';
export const CHECK_PASSWORD_RESET_CODE = 'CHECK_PASSWORD_RESET_CODE';
export const USER_GET_WELCOME_PAGE = 'USER_GET_WELCOME_PAGE';
export const USER_GET_WORKSHOPS = 'USER_GET_WORKSHOPS';
export const USER_GET_DASHBOARD = 'USER_GET_DASHBOARD';
export const USER_GET_TEAM = 'USER_GET_TEAM';
export const RESET_TEAM_HISTORY = 'RESET_TEAM_HISTORY';
export const USER_GET_INITIATIVES = 'USER_GET_INITIATIVES';
export const USER_GET_CHALLENGES = 'USER_GET_CHALLENGES';
export const USER_SAVE_INITIATIVES = 'USER_SAVE_INITIATIVES';
export const USER_SAVE_CHALLENGES = 'USER_SAVE_CHALLENGES';
export const USER_UPDATE_TEAM = 'USER_UPDATE_TEAM';
export const USER_GET_LEADERBOARD = 'USER_GET_LEADERBOARD';
export const USER_GET_ORG_CHART = 'USER_GET_ORG_CHART';
export const USER_SAVE_ORG_CHART = 'USER_SAVE_ORG_CHART';
export const USER_GET_SELF_ASSESSMENT = 'USER_GET_SELF_ASSESSMENT';
export const USER_SAVE_SELF_ASSESSMENT_ANSWERS = 'USER_SAVE_SELF_ASSESSMENT_ANSWERS';
export const USER_GET_SELF_ASSESSMENT_PDF = 'USER_GET_SELF_ASSESSMENTPDFS';
export const USER_GET_DECISION = 'USER_GET_DECISION';
export const ANALYZE_PNL = 'ANALYZE_PNL';
export const ANALYZE_TABLE = 'ANALYZE_TABLE';

// set client
export const SET_CLIENT = 'USER_SET_CLIENT';

export function signUp(formValues) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/sign-up`,
    data: {
      ...formValues,
    },
  });
  return {
    type: SIGNUP_USER,
    payload: request,
  };
}

export function forgotPassword(formValues) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/forgot-password`,
    data: {
      ...formValues,
    },
  });
  return {
    type: FORGOT_PASSWORD,
    payload: request,
  };
}

export function recoverPassword(formValues) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/recover-password`,
    data: {
      ...formValues,
    },
  });
  return {
    type: RECOVER_PASSWORD,
    payload: request,
  };
}

export function signIn(formValues) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/signin`,
    data: {
      ...formValues,
    },
  });
  return {
    type: SIGNIN_USER,
    payload: request,
  };
}

export function checkPasswordResetCode(id, code) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/check-reset-code/${id}/${code}`,
  });
  return {
    type: CHECK_PASSWORD_RESET_CODE,
    payload: request,
  };
}

export function signInUserSuccess(user) {
  return {
    type: SIGNIN_USER_SUCCESS,
    payload: user,
  };
}

export function signOut() {
  sessionStorage.removeItem('jwtToken');
  localStorage.removeItem('clientId');
  localStorage.clear();
  return {
    type: SIGNOUT_USER,
    payload: {},
  };
}

export function refreshToken(token) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/token/refresh`,
    data: {
      token,
    },
  });

  return {
    type: REFRESH_TOKEN,
    payload: request,
  };
}

export function refreshTokenSuccess(currentUser) {
  return {
    type: TOKEN_REFRESH_SUCCESS,
    payload: currentUser,
  };
}

export function refreshTokenFailure(error) {
  return {
    type: TOKEN_REFRESH_FAILURE,
    payload: error,
  };
}

export function resetToken() {
  return {
    type: RESET_TOKEN,
  };
}

export function getDashboard() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/dashboard`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_GET_DASHBOARD,
    payload: request,
  };
}

// tested, done
export function getTeam() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/team?clientId=${localStorage.getItem('clientId')}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });

  return {
    type: USER_GET_TEAM,
    payload: request,
  };
}

// tested, done
export function getWelcomePage() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/welcome-page?clientId=${localStorage.getItem('clientId')}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_GET_WELCOME_PAGE,
    payload: request,
  };
}

// tested, done
export function getWorkshops() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/workshops`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_GET_WORKSHOPS,
    payload: request,
  };
}

// tested, done
export function updateTeam(formData) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/user/team`,
    data: {
      ...formData,
      clientId: localStorage.getItem('clientId'),
    },
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_UPDATE_TEAM,
    payload: request,
  };
}

// tested, done
export function getInitiatives() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/strategic-initiatives?clientId=${localStorage.getItem('clientId')}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_GET_INITIATIVES,
    payload: request,
  };
}

// tested, done
export function saveInitiatives(values) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/user/strategic-initiatives`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...values,
      clientId: localStorage.getItem('clientId'),
    },
  });
  return {
    type: USER_SAVE_INITIATIVES,
    payload: request,
  };
}

// tested, done
export function getChallenges() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/challenges?clientId=${localStorage.getItem('clientId')}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_GET_CHALLENGES,
    payload: request,
  };
}

// tested, done
export function saveChallenges(values) {
  const request = axios({
    method: 'put',
    url: `${CONFIG.API_URL}/user/challenges`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      ...values,
      clientId: localStorage.getItem('clientId'),
    },
  });
  return {
    type: USER_SAVE_CHALLENGES,
    payload: request,
  };
}

// tested, done
export function getLeaderboard() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/leaderboard?clientId=${localStorage.getItem('clientId')}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_GET_LEADERBOARD,
    payload: request,
  };
}

// tested, done
export function getOrgChart() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/org-chart?clientId=${localStorage.getItem('clientId')}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_GET_ORG_CHART,
    payload: request,
  };
}

// tested, done
export function saveOrgChart(orgCharts) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/org-chart`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: { orgCharts, clientId: localStorage.getItem('clientId') },
  });
  return {
    type: USER_SAVE_ORG_CHART,
    payload: request,
  };
}

export function resetTeamHistory() {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/team/reset-history`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: RESET_TEAM_HISTORY,
    payload: request,
  };
}

// tested, done
export function getSelfAssessment() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/self-assessments?clientId=${localStorage.getItem('clientId')}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_GET_SELF_ASSESSMENT,
    payload: request,
  };
}

export function createSelfAssessmentAnswers(data) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/self-assessments`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: { ...data, clientId: localStorage.getItem('clientId') },
  });
  return {
    type: USER_SAVE_SELF_ASSESSMENT_ANSWERS,
    payload: request,
  };
}

// tested, done
export function exportSelfAssessmentPdf({ teamId }) {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/self-assessment-pdf?clientId=${localStorage.getItem('clientId')}&teamId=${teamId}`,
    responseType: 'arraybuffer',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/pdf',
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });

  return {
    type: USER_GET_SELF_ASSESSMENT_PDF,
    payload: request,
  };
}

export function getDecision() {
  const request = axios({
    method: 'get',
    url: `${CONFIG.API_URL}/user/decision?clientId=${localStorage.getItem('clientId')}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });
  return {
    type: USER_GET_DECISION,
    payload: request,
  };
}

export function analyzePNL(pnlDataArray, userSelections) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/analyze-pnl`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: {
      pnlDataArray,
      userSelections,
    },
  });
  return {
    type: ANALYZE_PNL,
    payload: request,
  };
}

export function createDecisionResult(payload) {
  const request = axios({
    method: 'POST',
    url: `${CONFIG.API_URL}/user/decision-results`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
      'Content-Type': 'application/json',
    },
    data: payload,
  });

  return {
    type: 'CREATE_DECISION_RESULT',
    payload: request,
  };
}

export function analyzeTable(data) {
  const request = axios({
    method: 'post',
    url: `${CONFIG.API_URL}/user/analyze-table`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
    data: { data },
  });
  return {
    type: ANALYZE_TABLE,
    payload: request,
  };
}

export function getUserDecisionResults(userId) {
  const request = axios({
    method: 'GET',
    url: `${CONFIG.API_URL}/user/decision-results?userId=${userId}`,
    headers: {
      Authorization: `bearer ${sessionStorage.getItem('jwtToken')}`,
    },
  });

  return {
    type: 'GET_USER_DECISION_RESULTS',
    payload: request,
  };
}
