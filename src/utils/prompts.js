/**
 * @typedef {Object} PromptParams
 * @property {any} pnlData - Single PnL data object for individual analysis
 * @property {Object} userSelections
 * @property {number} userSelections.totalFTE
 * @property {number} userSelections.totalInvestment
 */

export const SYSTEM_PROMPTS = {
  FINANCIAL_ANALYST:
    "You are a financial analyst AI. You must respond with ONLY a raw JSON object - no markdown formatting, no ```json tags, no additional text. The JSON must contain 'updatedPnL' and 'analysis' fields. Analyze each P&L data object individually and provide detailed financial analysis.",
};

const PNL_ANALYSIS_INSTRUCTIONS = `The analysis should explain the impact of the FTE and investment changes on the P&L.
Remember to respond with ONLY valid JSON - no additional text before or after.`;

const PNL_RESPONSE_EXAMPLE = `"updatedPnL": [
  [...existingHeaderRow, "Updated Value"],
  [...existingDataRow1, "125000"],
  [...existingDataRow2, "250000"],
  [...existingTotalRow, "375000"]
]`;

const PNL_FORMAT_NOTE = `Note: Your response should preserve all existing PNL columns and data exactly as provided, adding only the "Updated Value" column with your calculated adjustments based on the FTE and investment changes.`;

/**
 * @param {PromptParams} params
 * @returns {string}
 */
export const generatePNLAnalysisPrompt = ({ pnlData, userSelections }) => `
  You must respond with ONLY a valid JSON object in the following format:
  {
    "updatedPnL": [/* array of modified P&L rows */],
    "analysis": "Your detailed analysis here"
  }

  Analyze this P&L data and the proposed changes:

  P&L Data:
  ${JSON.stringify(pnlData, null, 2)}
  
  Proposed Changes:
  - FTE Change: ${userSelections.totalFTE} employees
  - Investment: $${userSelections.totalInvestment}
  
  ${PNL_ANALYSIS_INSTRUCTIONS}

  Here's an example of the expected response format:
  {
    ${PNL_RESPONSE_EXAMPLE},
    "analysis": "Based on the proposed changes of ${
      userSelections.totalFTE
    } FTE and $${
  userSelections.totalInvestment
} investment, the P&L shows significant improvements across all categories, with a total adjusted value of $375,000."
  }

  ${PNL_FORMAT_NOTE}
`;
