import express from 'express';
import USER_CONTROLLER from './controllers/user.js';
import ADMIN_CONTROLLER from './controllers/admin.js';
import TEAM_CONTROLLER from './controllers/team.js';
import CLIENT_CONTROLLER from './controllers/client.js';
import SELF_ASSESSMENT_CONTROLLER from './controllers/selfAssessment.js';
import LEADERBOARD_CONTROLLER from './controllers/leaderboard.js';
import ORG_CHART_CONTROLLER from './controllers/orgChart.js';
import INITIATIVE_CONTROLLER from './controllers/initiative.js';
import CHALLENGE_CONTROLLER from './controllers/challenge.js';
import FILES_CONTROLLER from './controllers/files.js';
import WELCOME_PAGE_CONTROLLER from './controllers/welcomePage.js';
import DECISION_CONTROLLER from './controllers/decision.js';
import DECISION_GROUPS_CONTROLLER from './controllers/decisionGroups.js';
import <PERSON><PERSON><PERSON><PERSON>I_CONTROLLER from './controllers/openai.js';
import CONF<PERSON> from '../config.json' with { type: 'json' };

import {
  Admin,
  Team,
  Client,
  Initiative,
  Challenge,
  LeaderboardScheme,
  OrgChartScheme,
  WelcomePage,
  SelfAssessment,
} from './models/index.js';

import jwtMiddleware from './lib/jwt_middleware.js';
import { validate } from 'express-validation';
import apiClient from './lib/api_client.js';
import DECISION_RESULTS_CONTROLLER from './controllers/decisionResults.js';

const router = express.Router();

// simulate network latency for testing UI updates
const latency = (req, res, next) => {
  if (CONFIG.latency) {
    setTimeout(() => {
      return next();
    }, CONFIG.latency);
  } else {
    console.log('no latency');
    return next();
  }
};

router.get(
  '/ping',
  function (req, res, next) {
    return next();
  },
  function (req, res, next) {
    return next();
  },
  function (req, res, next) {
    return apiClient.success(req, res, next, {});
  }
);

//admin endpoints - sign-in
router.post(
  '/admin/signin',
  latency,
  validate(Admin.validationSchema.login),
  ADMIN_CONTROLLER.signIn
);
router.post(
  '/admin/token/refresh',
  validate(Admin.validationSchema.tokenRefresh),
  ADMIN_CONTROLLER.refreshToken
);

//admin endpoints - team
router.get(
  '/admin/team/id/:id',
  jwtMiddleware,
  latency,
  validate(Team.validationSchema.get),
  TEAM_CONTROLLER.get
);
router.post(
  '/admin/team',
  jwtMiddleware,
  validate(Team.validationSchema.create),
  TEAM_CONTROLLER.create
);
router.put(
  '/admin/team',
  jwtMiddleware,
  validate(Team.validationSchema.update),
  TEAM_CONTROLLER.update
);
router.get(
  '/admin/team/all',
  jwtMiddleware,
  validate(Team.validationSchema.listAll),
  TEAM_CONTROLLER.listAll
);
router.put(
  '/admin/team/disabled/:id',
  jwtMiddleware,
  validate(Team.validationSchema.toggleDisabled),
  TEAM_CONTROLLER.toggleDisabled
);
router.get(
  '/admin/team/list',
  jwtMiddleware,
  latency,
  validate(Team.validationSchema.list),
  TEAM_CONTROLLER.list
);
router.get(
  '/admin/team/id/:id/export',
  validate(Team.validationSchema.exportExcel),
  jwtMiddleware,
  TEAM_CONTROLLER.exportExcel
);
router.get(
  '/admin/team/export',
  jwtMiddleware,
  validate(Team.validationSchema.exportAll),
  TEAM_CONTROLLER.exportExcelAll
);
router.post(
  '/admin/team/import',
  jwtMiddleware,
  latency,
  TEAM_CONTROLLER.import
);
router.post(
  '/admin/team/reset-history',
  jwtMiddleware,
  latency,
  TEAM_CONTROLLER.resetHistory
);

//admin endpoints - clients
router.get(
  '/admin/client/id/:id',
  jwtMiddleware,
  latency,
  validate(Client.validationSchema.get),
  CLIENT_CONTROLLER.get
);
router.post(
  '/admin/client',
  jwtMiddleware,
  validate(Client.validationSchema.create),
  CLIENT_CONTROLLER.create
);
router.put(
  '/admin/client',
  jwtMiddleware,
  validate(Client.validationSchema.update),
  CLIENT_CONTROLLER.update
);
router.put(
  '/admin/client/disabled/:id',
  jwtMiddleware,
  validate(Client.validationSchema.toggleDisabled),
  CLIENT_CONTROLLER.toggleDisabled
);
router.get(
  '/admin/client/list',
  jwtMiddleware,
  latency,
  validate(Client.validationSchema.list),
  CLIENT_CONTROLLER.list
);
router.get(
  '/admin/client/all',
  jwtMiddleware,
  validate(Client.validationSchema.listAll),
  CLIENT_CONTROLLER.listAll
);
router.get(
  '/admin/client/:id/export',
  jwtMiddleware,
  validate(Client.validationSchema.exportExcel),
  CLIENT_CONTROLLER.exportExcel
);
router.get(
  '/admin/client/:id/export-json',
  jwtMiddleware,
  validate(Client.validationSchema.exportExcel),
  CLIENT_CONTROLLER.exportData
);

//admin endpoints - self assessments
router.get(
  '/admin/self-assessments/id/:id',
  jwtMiddleware,
  latency,
  validate(SelfAssessment.validationSchema.get),
  SELF_ASSESSMENT_CONTROLLER.get
);
router.post(
  '/admin/self-assessments',
  jwtMiddleware,
  validate(SelfAssessment.validationSchema.create),
  SELF_ASSESSMENT_CONTROLLER.create
);
router.put(
  '/admin/self-assessments',
  jwtMiddleware,
  validate(SelfAssessment.validationSchema.update),
  SELF_ASSESSMENT_CONTROLLER.update
);
router.put(
  '/admin/self-assessments/disabled/:id',
  jwtMiddleware,
  validate(SelfAssessment.validationSchema.toggleDisabled),
  SELF_ASSESSMENT_CONTROLLER.toggleDisabled
);
router.get(
  '/admin/self-assessments/list',
  jwtMiddleware,
  latency,
  validate(SelfAssessment.validationSchema.list),
  SELF_ASSESSMENT_CONTROLLER.list
);
router.get(
  '/admin/self-assessments/all',
  jwtMiddleware,
  validate(SelfAssessment.validationSchema.listAll),
  SELF_ASSESSMENT_CONTROLLER.listAll
);

//admin endpoints - leaderboard
router.get(
  '/admin/leaderboard/id/:id',
  jwtMiddleware,
  latency,
  validate(LeaderboardScheme.validationSchema.get),
  LEADERBOARD_CONTROLLER.get
);
router.post(
  '/admin/leaderboard',
  jwtMiddleware,
  validate(LeaderboardScheme.validationSchema.create),
  LEADERBOARD_CONTROLLER.create
);
router.put(
  '/admin/leaderboard',
  jwtMiddleware,
  validate(LeaderboardScheme.validationSchema.update),
  LEADERBOARD_CONTROLLER.update
);
router.put(
  '/admin/leaderboard/disabled/:id',
  jwtMiddleware,
  validate(LeaderboardScheme.validationSchema.toggleDisabled),
  LEADERBOARD_CONTROLLER.toggleDisabled
);
router.get(
  '/admin/leaderboard/list',
  jwtMiddleware,
  latency,
  validate(LeaderboardScheme.validationSchema.list),
  LEADERBOARD_CONTROLLER.list
);
router.get(
  '/admin/leaderboard/all',
  jwtMiddleware,
  validate(LeaderboardScheme.validationSchema.listAll),
  LEADERBOARD_CONTROLLER.listAll
);

//admin endpoints - welcome page
router.get(
  '/admin/welcome-page/id/:id',
  jwtMiddleware,
  latency,
  validate(WelcomePage.validationSchema.get),
  WELCOME_PAGE_CONTROLLER.get
);
router.post(
  '/admin/welcome-page',
  jwtMiddleware,
  validate(WelcomePage.validationSchema.create),
  WELCOME_PAGE_CONTROLLER.create
);
router.put(
  '/admin/welcome-page',
  jwtMiddleware,
  validate(WelcomePage.validationSchema.update),
  WELCOME_PAGE_CONTROLLER.update
);
router.put(
  '/admin/welcome-page/disabled/:id',
  jwtMiddleware,
  validate(WelcomePage.validationSchema.toggleDisabled),
  WELCOME_PAGE_CONTROLLER.toggleDisabled
);
router.get(
  '/admin/welcome-page/list',
  jwtMiddleware,
  latency,
  validate(WelcomePage.validationSchema.list),
  WELCOME_PAGE_CONTROLLER.list
);
router.get(
  '/admin/welcome-page/all',
  jwtMiddleware,
  validate(WelcomePage.validationSchema.listAll),
  WELCOME_PAGE_CONTROLLER.listAll
);

//admin endpoints - org chart
router.get(
  '/admin/org-chart/id/:id',
  jwtMiddleware,
  latency,
  validate(OrgChartScheme.validationSchema.get),
  ORG_CHART_CONTROLLER.get
);
router.post(
  '/admin/org-chart',
  jwtMiddleware,
  validate(OrgChartScheme.validationSchema.create),
  ORG_CHART_CONTROLLER.create
);
router.put(
  '/admin/org-chart',
  jwtMiddleware,
  validate(OrgChartScheme.validationSchema.update),
  ORG_CHART_CONTROLLER.update
);
router.put(
  '/admin/org-chart/disabled/:id',
  jwtMiddleware,
  validate(OrgChartScheme.validationSchema.toggleDisabled),
  ORG_CHART_CONTROLLER.toggleDisabled
);
router.get(
  '/admin/org-chart/list',
  jwtMiddleware,
  latency,
  validate(OrgChartScheme.validationSchema.list),
  ORG_CHART_CONTROLLER.list
);
router.get(
  '/admin/org-chart/all',
  jwtMiddleware,
  validate(OrgChartScheme.validationSchema.listAll),
  ORG_CHART_CONTROLLER.listAll
);

//admin endpoints - strategic initiatives
router.get(
  '/admin/initiative/id/:id',
  jwtMiddleware,
  latency,
  validate(Initiative.validationSchema.get),
  INITIATIVE_CONTROLLER.get
);
router.post(
  '/admin/initiative',
  jwtMiddleware,
  validate(Initiative.validationSchema.create),
  INITIATIVE_CONTROLLER.create
);
router.put(
  '/admin/initiative',
  jwtMiddleware,
  validate(Initiative.validationSchema.update),
  INITIATIVE_CONTROLLER.update
);
router.put(
  '/admin/initiative/disabled/:id',
  validate(Initiative.validationSchema.toggleDisabled),
  jwtMiddleware,
  INITIATIVE_CONTROLLER.toggleDisabled
);
router.get(
  '/admin/initiative/list',
  jwtMiddleware,
  latency,
  validate(Initiative.validationSchema.list),
  INITIATIVE_CONTROLLER.list
);
router.get(
  '/admin/initiative/all',
  jwtMiddleware,
  validate(Initiative.validationSchema.listAll),
  INITIATIVE_CONTROLLER.listAll
);

//admin endpoints - challenges - testing done
router.get(
  '/admin/challenge/id/:id',
  jwtMiddleware,
  latency,
  validate(Challenge.validationSchema.get),
  CHALLENGE_CONTROLLER.get
);
router.post(
  '/admin/challenge',
  jwtMiddleware,
  validate(Challenge.validationSchema.create),
  CHALLENGE_CONTROLLER.create
);
router.put(
  '/admin/challenge',
  jwtMiddleware,
  validate(Challenge.validationSchema.update),
  CHALLENGE_CONTROLLER.update
);
router.put(
  '/admin/challenge/disabled/:id',
  jwtMiddleware,
  validate(Challenge.validationSchema.toggleDisabled),
  CHALLENGE_CONTROLLER.toggleDisabled
);
router.get(
  '/admin/challenge/list',
  jwtMiddleware,
  latency,
  validate(Challenge.validationSchema.list),
  CHALLENGE_CONTROLLER.list
);
router.get(
  '/admin/challenge/all',
  jwtMiddleware,
  validate(Challenge.validationSchema.listAll),
  CHALLENGE_CONTROLLER.listAll
);

router.get(
  '/admin/self-assessment-pdf',
  latency,
  SELF_ASSESSMENT_CONTROLLER.exportPDF
);

//user endpoints
router.post('/user/signin', latency, USER_CONTROLLER.signIn);
router.post('/user/sign-up', latency, USER_CONTROLLER.signUp);
router.post('/user/token/refresh', latency, USER_CONTROLLER.refreshToken);
router.post('/user/forgot-password', latency, USER_CONTROLLER.forgotPassword);
router.post('/user/recover-password', latency, USER_CONTROLLER.recoverPassword);
router.get('/user/dashboard', jwtMiddleware, latency, USER_CONTROLLER.dashboard);
router.get('/user/team', jwtMiddleware, latency, USER_CONTROLLER.getTeamDetails);
router.get('/user/decision', jwtMiddleware, latency, USER_CONTROLLER.getDecision);
router.put(
  '/user/team',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.updateTeamDetails
);
router.post(
  '/user/team/reset-history',
  jwtMiddleware,
  latency,
  TEAM_CONTROLLER.resetHistory
);
router.get(
  '/user/strategic-initiatives',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.getInitiatives
);
router.put(
  '/user/strategic-initiatives',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.saveInitiatives
);
router.get(
  '/user/challenges',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.getChallenges
);
router.put(
  '/user/challenges',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.saveChallenges
);
router.get(
  '/user/leaderboard',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.getLeaderboard
);
router.get(
  '/user/org-chart',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.getOrgChart
);
router.post(
  '/user/org-chart',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.saveOrgChart
);
router.get(
  '/user/welcome-page',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.getWelcomePage
);
router.get(
  '/user/workshops',
  jwtMiddleware,
  latency,
  USER_CONTROLLER.getAllAvailableWorkshops
);
router.get(
  '/user/self-assessments',
  jwtMiddleware,
  latency,
  SELF_ASSESSMENT_CONTROLLER.getSelfAssesmentsQuestions
);
router.post(
  '/user/self-assessments',
  jwtMiddleware,
  latency,
  SELF_ASSESSMENT_CONTROLLER.createAnswers
);
router.get(
  '/user/self-assessment-pdf',
  // jwtMiddleware,
  latency,
  // validate(Challenge.validationSchema.get),
  SELF_ASSESSMENT_CONTROLLER.exportPDF
);

//Other
router.post(
  '/upload-file',
  jwtMiddleware,
  latency,
  FILES_CONTROLLER.uploadFile
);

// Decision routes
router.post(
  '/admin/decision',
  jwtMiddleware,
  DECISION_CONTROLLER.create
);
router.get(
  '/admin/decision/list',
  jwtMiddleware,
  DECISION_CONTROLLER.list
);

router.get(
  '/admin/decision/id/:id',
  jwtMiddleware,
  DECISION_CONTROLLER.get
);
router.put(
  '/admin/decision',
  jwtMiddleware,
  DECISION_CONTROLLER.update
);
// router.put(
//   '/admin/decision/disabled/:id',
//   jwtMiddleware,
//   DECISION_CONTROLLER.toggleDisabled
// );

// Decision Groups routes
router.get(
  '/admin/decision-groups',
  jwtMiddleware,
  DECISION_GROUPS_CONTROLLER.listAll
);
router.get(
  '/admin/decision-groups/id/:id',
  jwtMiddleware,
  DECISION_GROUPS_CONTROLLER.get
);
router.post(
  '/admin/decision-groups',
  jwtMiddleware,
  DECISION_GROUPS_CONTROLLER.create
);
router.delete(
  '/admin/decision-groups/id/:id',
  jwtMiddleware,
  DECISION_GROUPS_CONTROLLER.delete
);

router.post(
  '/user/analyze-pnl',
  jwtMiddleware,
  latency,
  OPENAI_CONTROLLER.analyzePNL
);

// Decision Results routes
router.post(
  '/user/decision-results',
  jwtMiddleware,
  latency,
  DECISION_RESULTS_CONTROLLER.store
);

router.get(
  '/user/decision-results',
  jwtMiddleware,
  latency,
  DECISION_RESULTS_CONTROLLER.getUserResults
);

router.get(
  '/user/decision-results/:id',
  jwtMiddleware,
  latency,
  DECISION_RESULTS_CONTROLLER.getResult
);

router.post(
  '/user/analyze-table',
  jwtMiddleware,
  latency,
  OPENAI_CONTROLLER.analyzeTable
);



export default router;
